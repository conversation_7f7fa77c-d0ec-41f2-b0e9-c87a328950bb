"""
**************************************
*  <AUTHOR>   毕纪波
*  @Time    ：   2025/6/27 12:44
*  @Project :   accumsand_service
*  @FileName:   day06.py
*  @description: 
**************************************
"""
# 迭代器与生成器
from collections.abc import Iterable, Iterator
#
# a = [1, 2]
# print(isinstance(a, Iterable))
# print(isinstance(a, Iterator))
#
#
# class MyIterator(Iterator):
#     def __init__(self, demo):
#         self.demo_list = demo
#         self.index = 0
#
#     def __next__(self):
#         try:
#             word = self.demo_list[self.index]
#         except IndexError:
#             raise StopIteration
#         self.index += 1
#         return word


def gen_func():
    yield 1

if __name__ == '__main__':
    gen =gen_func()
    print(gen)
