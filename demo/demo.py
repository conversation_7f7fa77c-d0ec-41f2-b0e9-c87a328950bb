"""
**************************************
*  <AUTHOR>   毕纪波
*  @Time    ：   2025/6/23 15:19
*  @Project :   accumsand_service
*  @FileName:   demo.py
*  @description: 
**************************************
"""

def replace_big_number_incrementally(filepath, target_number, output_path=None):
    """
    将文件中匹配的长整数（如 ID）逐一替换为递增的值，从指定值开始。

    参数:
        filepath (str): 输入文件路径。
        target_number (int or str): 初始目标数字，从它开始递增替换。
        output_path (str): 输出文件路径（默认覆盖原文件）。
    """
    target_number = str(target_number)
    current_value = int(target_number)
    updated_lines = []

    with open(filepath, 'r', encoding='utf-8') as f:
        for line in f:
            while target_number in line:
                line = line.replace(target_number, str(current_value), 1)
                current_value += 1
            updated_lines.append(line)

    target_path = output_path if output_path else filepath
    with open(target_path, 'w', encoding='utf-8') as f:
        f.writelines(updated_lines)

    print(f"替换完成，从 {target_number} 到 {current_value - 1}，输出路径：{target_path}")



replace_big_number_incrementally('测试霸王-0623-发券_new.csv', '1083636731356820100',"测试霸王-0623-发券_2.csv")