"""
**************************************
*  <AUTHOR>   毕纪波
*  @Time    ：   2025/6/19 12:36
*  @Project :   accumsand_service
*  @FileName:   day04.py
*  @description: 
**************************************
"""
import numbers

# 属性查找入口
"""
__getattr__:查找不到类属性的时候，会进入__getattr__
__getattribute__:访问属性时，直接进入__getattribute__
"""


# 属性描述符
class IntField:
    def __get__(self, instance, owner):
        return self.value

    def __set__(self, instance, value):
        if not isinstance(value, numbers.Integral):
            raise ValueError("int value need")
        self.value = value

    def __delete__(self, instance):
        pass


class User:
    age = IntField()


def say(self):
    return 'say'


# 元类：是创建类的类
A = type(
    "User",
    (),
    {'say': say}
)

a = A()
print(a.say())


class MetaClass(type):
    def __new__(cls, *args, **kwargs):
        pass


class User1(metaclass=MetaClass):
    pass


user = User1()
