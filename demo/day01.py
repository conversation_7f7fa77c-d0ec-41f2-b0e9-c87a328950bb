"""
**************************************
*  <AUTHOR>   毕纪波
*  @Time    ：   2025/6/5 17:35
*  @Project :   accumsand_service
*  @FileName:   day01.py
*  @description: 
**************************************
"""
import contextlib

"""
什么场景下会使用到抽象基类:
1在某些情况下，希望判断某个对象的类型
2实现框架时，需要子类 必须要实现某些方法
"""


# 上下文管理器协议
class Sample:
    def __init__(self):
        self.a = 10
        print("init")
    def __enter__(self):
        print("enter")
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        print("exit")

    def do_something(self):
        self.a = 20
        print("do")

with Sample() as sam:
    sam.do_something()


@contextlib.contextmanager
def file_open(file_name):
    print("file open")
    yield {}
    print("file end")

with file_open("boby.txt") as f:
    print("file processing")