"""
**************************************
*  <AUTHOR>   毕纪波
*  @Time    ：   2025/6/23 22:22
*  @Project :   accumsand_service
*  @FileName:   day05.py
*  @description: 
**************************************
"""

class IntField:
    def


class ModelMetaClass(type):

    def __new__(cls, attrs, *args, **kwargs):
        fields = {}
        for key, value in attrs.items():
            pass


class User(metaclass=ModelMetaClass):
    pass


type(
    "User",
    (),
    {}
)


class UserModel:
    user = Model.IntField()
