import threading
import time

from pandas.core.computation.expressions import where

"""
**************************************
*  <AUTHOR>   毕纪波
*  @Time    ：   2025/7/2 13:19
*  @Project :   accumsand_service
*  @FileName:   demo08.py
*  @description: 
**************************************
"""

"""
线程之间的通信方式：
1 共享变量
"""
detail_url_list = []


def get_detail(url):
    global detail_url_list
    # 爬取详情
    while True:
        if len(detail_url_list):
            print("get_detail_started")
            time.sleep(2)
            url = detail_url_list.pop()
            print("get_detail_end")


def get_url(url):
    global detail_url_list
    # 爬取地址
    while True:
        print("get_url_started")
        time.sleep(2)
        for i in range(20):
            detail_url_list.append(f"http://www.baidu.com/{i}")
        print("get_url_end")


if __name__ == '__main__':
    for i in range(10):
        thread1 = threading.Thread(target=get_url)
