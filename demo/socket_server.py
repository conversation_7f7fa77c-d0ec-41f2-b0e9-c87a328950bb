"""
**************************************
*  <AUTHOR>   毕纪波
*  @Time    ：   2025/7/1 12:15
*  @Project :   accumsand_service
*  @FileName:   socket_demo.py
*  @description: 
**************************************
"""
import socket

server = socket.socket(socket.AF_INET, socket.SOCK_STREAM)  # Create socket object
server.bind(('0.0.0.0', 8000))  # Bind to address and port

server.listen(1)  # Listen for incoming connections, maximum 1 connection

print("Waiting for connection...")
sock, addr = server.accept()  # Accept incoming connection

print(f"Connected by {addr}")

data = sock.recv(1024)  # Receive data from client
print(f"Received: {data.decode('utf-8')}")

sock.send(b"Hello from server!")  # Send data to client

sock.close()  # Close the client socket
server.close()  # Close the server socket
