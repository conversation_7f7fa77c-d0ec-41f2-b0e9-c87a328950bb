"""
**************************************
*  <AUTHOR>   毕纪波
*  @Time    ：   2025/7/1 13:22
*  @Project :   accumsand_service
*  @FileName:   demo07.py
*  @description: 
**************************************
"""
import threading
import time

from typing_inspection.typing_objects import target

# gil: global interperter lock
"""
python中的一个线程对应于C语言中的一个线程
gil使得同一时刻只有一个线程再一个CPU上执行字节码，无法将多个线程映射到多个CPU执行
gil会根据执行的字节码行数以及时间片释放gil；gil也会在遇到IO操作时，会主动释放(pyton非常适合频繁IO场景下的多线程)

对于IO操作来说，多线程和多进程性能差别不大
多线程编程的方式：
1通过Thread类实例化
2通过集成Thread来实现
"""


def get_detail(url):
    print("get_detail_started")
    time.sleep(2)
    print("get_detail_end")


def get_url(url):
    print("get_url_started")
    time.sleep(2)
    print("get_url_end")


class GetDetaill(threading.Thread):
    def __init__(self, name):
        return super().__init__(name=name)

    def run(self):
        print("get_detail_started")
        time.sleep(2)
        print("get_detail_end")


if __name__ == '__main__':
    thread1 = threading.Thread(target=get_detail, args=("",))
    thread2 = threading.Thread(target=get_url, args=("",))
    thread3 = GetDetaill("thread3")
    thread3.daemon = True
    start_time = time.time()
    thread1.start()
    thread2.start()
    thread3.start()
    thread1.join()  # 等加入的所有子进程结束后，再执行子进程
    thread2.join()
    thread3.join()
    print(f"last time:{(time.time() - start_time)}")
