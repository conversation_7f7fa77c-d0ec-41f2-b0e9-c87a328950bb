"""
**************************************
*  <AUTHOR>   毕纪波
*  @Time    ：   2025/6/17 13:23
*  @Project :   accumsand_service
*  @FileName:   day02.py
*  @description: 
**************************************
"""


def hadle_item(item):
    return item * item


# 列表生成式:性能高于列表操作
list_demo = [hadle_item(i) for i in range(0, 20) if i % 2 == 0]

# 生成器表达式
generator_demo = (hadle_item(i) for i in range(0, 20) if i % 2 == 0)
print(type(generator_demo))

# 字典生成式
dict_demo = {"lilei": 18, "wangmeimei": 19, "zhanghao": 20}
reversed_dict = {value: key for key, value in dict_demo.items()}
print(reversed_dict)

# 集合生成式：
set_demo = {key for key, value in dict_demo.items()}
print(set_demo)
