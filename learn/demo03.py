"""
**************************************
*  <AUTHOR>   毕纪波
*  @Time    ：   2025/7/3 12:21
*  @Project :   accumsand_service
*  @FileName:   demo03.py
*  @description: 
**************************************
"""
import time
from concurrent import futures
from concurrent.futures.thread import ThreadPoolExecutor

"""
为什么要使用线程池：
"""


# def get_html(times):
#     time.sleep(times)
#     print(f"get page {times} success")
#     return times
#
#
# executor = ThreadPoolExecutor(max_workers=8)
# # task1 = executor.submit(get_html, 3)
# # task2 = executor.submit(get_html, 2)
#
#
# for future in executor.map(get_html, [1, 2, 4, 5, 6, 7, 8]):
#     data = future.result()
#     print(data)


def open_file():
    with open(r"E:\accumsand_service\demo\测试霸王-0623-发券.csv",encoding="utf8") as file:
        for each in file:
            lines = file.readlines()
            time.sleep(50)

    return True



if __name__ == '__main__':
    executor = ThreadPoolExecutor(max_workers=20)
    for _ in range(1000):
        executor.submit(open_file)
