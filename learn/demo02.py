"""
**************************************
*  <AUTHOR>   毕纪波
*  @Time    ：   2025/7/3 12:06
*  @Project :   accumsand_service
*  @FileName:   demo02.py
*  @description: 
**************************************
"""
import threading
import time

"""
Semaphore 是用于控制进入数量的锁
文件，读写，写一般只是用于一个线程写，读可以允许有多个
"""


class HtmlSpider(threading.Thread):

    def __init__(self, url, sem: threading.Semaphore):
        super().__init__()
        self.url = url
        self.sem = sem

    def run(self):
        time.sleep(2)
        print("get html text success")
        self.sem.release()


class UrlProducer(threading.Thread):
    def __init__(self, sem: threading.Semaphore):
        super().__init__()
        self.sem = sem

    def run(self):
        for i in range(20):
            self.sem.acquire()
            html_thread = HtmlSpider(f"https://www.baidu.com/{str(i)}", self.sem)
            html_thread.start()


if __name__ == '__main__':
    sem = threading.Semaphore(3)
    url_producer = UrlProducer(sem=sem)
    url_producer.start()
