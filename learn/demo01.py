"""
**************************************
*  <AUTHOR>   毕纪波
*  @Time    ：   2025/7/1 15:35
*  @Project :   accumsand_service
*  @FileName:   demo01.py
*  @description: 
**************************************
"""
from typing import Optional

from fastapi import FastAPI
from pydantic import BaseModel, field_validator
from pydantic.v1 import BaseSettings

app = FastAPI()


class User(BaseModel):
    id: int
    name: str = None
    email: Optional[str] = None

    @field_validator("name")
    def name_size_validator(cls, name):
        if len(name) > 6:
            raise ValueError("name must be less 6")
        return name


user = User(id=123, name="123456", email="123")
print(user.model_dump())
print(user.model_dump_json())
print(user.model_fields)


class AppConfig(BaseSettings):
    app_name: str = "Myapp"

    class Config:
        env_file = ".env"

config = AppConfig()
print(config.app_name)
